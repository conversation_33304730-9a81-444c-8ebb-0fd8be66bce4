// Monitor voor echte AI predictions
// Run dit in de browser console

console.log('🔍 Starting Real AI Predictions Monitor...');

let monitorInterval;
let lastPredictions = {};
let checkCount = 0;

function startMonitoring() {
  console.log('📊 Monitoring localStorage for AI predictions...');
  
  monitorInterval = setInterval(() => {
    checkCount++;
    
    // Check predictions
    const predictions = JSON.parse(localStorage.getItem('cts_predictions_v1') || '{}');
    const analysisStates = JSON.parse(localStorage.getItem('cts_analysis_states_v1') || '{}');
    
    console.log(`[${checkCount}] Analysis States:`, analysisStates);
    console.log(`[${checkCount}] Predictions Count:`, Object.keys(predictions).length);
    
    // Check for new predictions
    Object.entries(predictions).forEach(([symbol, prediction]) => {
      if (prediction && (!lastPredictions[symbol] || 
          JSON.stringify(prediction) !== JSON.stringify(lastPredictions[symbol]))) {
        
        console.log(`🎉 NEW AI PREDICTION for ${symbol}:`);
        console.log(`  Prediction: ${prediction.prediction}`);
        console.log(`  Confidence: ${prediction.confidence}`);
        console.log(`  Reasoning: ${prediction.reasoning}`);
        console.log(`  Full prediction:`, prediction);
        
        // Check if it appears in UI
        setTimeout(() => {
          const assetCards = document.querySelectorAll('[class*="asset"]');
          console.log(`UI Check: Found ${assetCards.length} asset cards`);
          
          // Look for prediction chips
          const predictionChips = document.querySelectorAll('[class*="bg-green"], [class*="bg-red"], [class*="bg-yellow"]');
          console.log(`UI Check: Found ${predictionChips.length} prediction chips`);
          
          if (predictionChips.length > 0) {
            console.log('✅ Predictions are visible in UI!');
          } else {
            console.log('⚠️ Predictions not yet visible in UI');
          }
        }, 1000);
      }
    });
    
    lastPredictions = { ...predictions };
    
    // Stop after 10 minutes
    if (checkCount >= 60) {
      clearInterval(monitorInterval);
      console.log('⏰ Monitoring stopped after 10 minutes');
    }
  }, 10000); // Check every 10 seconds
  
  console.log('🔄 Monitoring started - checking every 10 seconds for 10 minutes...');
}

function checkCurrentStatus() {
  console.log('\n📋 Current Status Check:');
  
  // Check data
  const assets = JSON.parse(localStorage.getItem('cts_assets_v1') || '[]');
  const candles1m = JSON.parse(localStorage.getItem('cts_c1m_v1') || '{}');
  const candles1h = JSON.parse(localStorage.getItem('cts_c1h_v1') || '{}');
  const predictions = JSON.parse(localStorage.getItem('cts_predictions_v1') || '{}');
  const analysisStates = JSON.parse(localStorage.getItem('cts_analysis_states_v1') || '{}');
  
  console.log('Assets:', assets.length);
  console.log('1m Candles:', Object.keys(candles1m).map(k => `${k}: ${candles1m[k]?.length || 0}`));
  console.log('1h Candles:', Object.keys(candles1h).map(k => `${k}: ${candles1h[k]?.length || 0}`));
  console.log('Analysis States:', analysisStates);
  console.log('Predictions:', Object.keys(predictions).length);
  
  if (Object.keys(predictions).length > 0) {
    console.log('🎯 Current Predictions:');
    Object.entries(predictions).forEach(([symbol, prediction]) => {
      if (prediction) {
        console.log(`  ${symbol}: ${prediction.prediction} (${prediction.confidence})`);
      }
    });
  }
  
  // Check if ready for AI analysis
  const symbols = ['XRP-EUR', 'SOL-EUR', 'DOGE-EUR'];
  symbols.forEach(symbol => {
    const candles1mCount = candles1m[symbol]?.length || 0;
    const candles1hCount = candles1h[symbol]?.length || 0;
    const state = analysisStates[symbol] || 'unknown';
    const ready = candles1mCount >= 10 && candles1hCount >= 3 && state !== 'ANALYZING';
    
    console.log(`${symbol}: ${ready ? '✅' : '❌'} Ready (1m: ${candles1mCount}, 1h: ${candles1hCount}, state: ${state})`);
  });
}

function stopMonitoring() {
  if (monitorInterval) {
    clearInterval(monitorInterval);
    console.log('🛑 Monitoring stopped');
  }
}

// Auto-start
checkCurrentStatus();
startMonitoring();

// Export to global scope
window.predictionMonitor = {
  startMonitoring,
  stopMonitoring,
  checkCurrentStatus
};

console.log('✅ Prediction Monitor started!');
console.log('📝 Available functions:');
console.log('  - predictionMonitor.checkCurrentStatus() - Check current status');
console.log('  - predictionMonitor.stopMonitoring() - Stop monitoring');
console.log('  - predictionMonitor.startMonitoring() - Restart monitoring');
