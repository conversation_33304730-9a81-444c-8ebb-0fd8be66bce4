
import React from 'react';
import { Trade } from '../types';
import { ArrowUpRight, ArrowDownLeft } from 'lucide-react';

const TradeLog: React.FC<{ trades: Trade[] }> = ({ trades }) => {
  const fmtTime = (dt: Date) =>
    new Date(dt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  return (
    <div className="bg-gray-800/60 rounded-xl p-4 border border-gray-700/30">
      <div className="text-xs uppercase tracking-wider text-gray-400 mb-3">Transacties</div>
      <div className="overflow-y-auto max-h-72 space-y-2 pr-1">
        {trades.length === 0 ? (
          <p className="text-center text-sm text-gray-500 py-6">Nog geen transacties.</p>
        ) : (
          trades.map((trade) => {
            const isBuy = trade.type === 'BUY';
            return (
              <div
                key={trade.id}
                className="bg-gray-800/40 border border-gray-700/20 px-3 py-2 rounded-lg flex justify-between items-center"
              >
                <div className="flex items-center gap-2">
                  {isBuy ? (
                    <ArrowDownLeft className="text-green-400 h-4 w-4" />
                  ) : (
                    <ArrowUpRight className="text-red-400 h-4 w-4" />
                  )}
                  <div className="leading-tight">
                    <div className="font-semibold text-white text-sm">
                      <span className={isBuy ? 'text-green-400' : 'text-red-400'}>{trade.type}</span>{' '}
                      {trade.symbol}
                    </div>
                    <div className="text-[11px] text-gray-500">{fmtTime(trade.timestamp)}</div>
                  </div>
                </div>
                <div className="text-right font-mono text-xs leading-tight">
                  <div className="text-white">
                    {trade.amount.toFixed(4)} @ €{trade.price.toFixed(3)}
                  </div>
                  {trade.profit !== undefined && (
                    <div className={trade.profit >= 0 ? 'text-green-400' : 'text-red-400'}>
                      {trade.profit >= 0 ? '+' : ''}€{trade.profit.toFixed(2)}
                    </div>
                  )}
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default TradeLog;