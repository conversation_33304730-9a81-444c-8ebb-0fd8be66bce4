
import React from 'react';
import { Portfolio } from '../types';
import { Wallet, Landmark, TrendingUp, TrendingDown } from 'lucide-react';

const PortfolioPanel: React.FC<{ portfolio: Portfolio }> = ({ portfolio }) => {
  const { totalValue, pl, plPercent, balance, holdings, initialValue } = portfolio;
  const isProfit = pl >= 0;

  const StatCard: React.FC<{ icon: React.ReactNode, label: string, value: string, valueColor?: string, className?: string }> = ({ icon, label, value, valueColor, className }) => (
    <div className={`flex items-center gap-4 p-3 rounded-lg bg-gray-700/50 ${className}`}>
      <div className="text-teal-400">{icon}</div>
      <div>
        <div className="text-xs text-gray-400">{label}</div>
        <div className={`text-sm font-bold ${valueColor}`}>{value}</div>
      </div>
    </div>
  );

  return (
    <div className="bg-gray-800/60 rounded-xl p-4 border border-gray-700/30 space-y-4">
      <div className="text-xs uppercase tracking-wider text-gray-400">Portfolio</div>

      <div className="bg-gray-900/40 rounded-lg p-4 border border-gray-700/20 text-center">
        <div className="text-xs text-gray-400">Totale Waarde</div>
        <div className="text-3xl font-extrabold text-white tracking-tight">€{totalValue.toFixed(2)}</div>
        <div className={`flex items-center justify-center gap-2 text-sm font-semibold ${isProfit ? 'text-green-400' : 'text-red-400'}`}>
          {isProfit ? <TrendingUp size={16} /> : <TrendingDown size={16} />}
          <span>{pl.toFixed(2)} EUR ({plPercent.toFixed(2)}%)</span>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <StatCard icon={<Landmark size={18}/>} label="Startkapitaal" value={`€${initialValue.toFixed(2)}`} />
        <StatCard icon={<Wallet size={18}/>} label="Saldo" value={`€${balance.toFixed(2)}`} valueColor="text-white"/>
      </div>

      <div>
        <div className="text-sm font-semibold mb-2 text-gray-300">Activa</div>
        <div className="space-y-2">
          {holdings.length > 0 ? holdings.map(holding => (
            <div key={holding.symbol} className="bg-gray-800/40 border border-gray-700/20 p-3 rounded-lg flex items-center justify-between">
              <div>
                <div className="font-semibold text-white text-sm">{holding.symbol}</div>
                <div className="text-[11px] text-gray-400">{holding.amount.toFixed(6)}</div>
              </div>
              <div className="text-right">
                <div className="font-mono text-white text-sm">€{holding.value.toFixed(2)}</div>
              </div>
            </div>
          )) : (
            <div className="text-center text-sm text-gray-500 py-4">Geen activa in bezit.</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PortfolioPanel;