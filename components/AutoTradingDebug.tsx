import React, { useState, useEffect } from 'react';
import { Bug, Play, Pause, RefreshCw, AlertCircle, CheckCircle, Clock } from 'lucide-react';

interface AutoTradingDebugProps {
  autoTradeEnabled: boolean;
  predictions: Record<string, any>;
  lastActions: Record<string, any>;
  portfolio: any;
  assets: any[];
  candleHistories1m: Record<string, any[]>;
  candleHistories1h: Record<string, any[]>;
  onToggleAutoTrade: () => void;
}

const AutoTradingDebug: React.FC<AutoTradingDebugProps> = ({
  autoTradeEnabled,
  predictions,
  lastActions,
  portfolio,
  assets,
  candleHistories1m,
  candleHistories1h,
  onToggleAutoTrade
}) => {
  const [debugLogs, setDebugLogs] = useState<string[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  useEffect(() => {
    if (!isMonitoring) return;

    const interval = setInterval(() => {
      // Monitor voor nieuwe predictions
      Object.entries(predictions).forEach(([symbol, prediction]) => {
        if (prediction) {
          addLog(`${symbol}: ${prediction.prediction} (conf: ${prediction.confidence?.toFixed(2)})`);
        }
      });
    }, 5000);

    return () => clearInterval(interval);
  }, [isMonitoring, predictions]);

  const testAutoTrading = () => {
    addLog('🧪 Starting auto-trading test...');
    
    // Check prerequisites
    if (!autoTradeEnabled) {
      addLog('❌ Auto-trading is disabled');
      return;
    }

    const hasApiKey = !!(import.meta as any).env?.VITE_DEEPSEEK_API_KEY;
    if (!hasApiKey) {
      addLog('❌ DeepSeek API key missing');
      return;
    }

    addLog('✅ Auto-trading enabled');
    addLog('✅ API key configured');
    addLog(`💰 Portfolio balance: €${portfolio.balance?.toFixed(2)}`);
    addLog(`📊 Active assets: ${assets.length}`);

    // Check data availability
    assets.forEach(asset => {
      if (asset.price > 0) {
        addLog(`📈 ${asset.symbol}: €${asset.price.toFixed(4)}`);
      } else {
        addLog(`⚠️ ${asset.symbol}: No price data`);
      }
    });

    // Check candle data
    Object.entries(candleHistories1m).forEach(([symbol, candles]) => {
      const count1m = candles?.length || 0;
      const count1h = candleHistories1h[symbol]?.length || 0;
      addLog(`📊 ${symbol}: ${count1m} 1m candles, ${count1h} 1h candles`);

      if (count1m < 10 || count1h < 3) {
        addLog(`⚠️ ${symbol}: Insufficient data for AI analysis`);
      } else {
        addLog(`✅ ${symbol}: Ready for AI analysis`);
      }
    });

    // Check recent predictions
    const predictionCount = Object.keys(predictions).length;
    addLog(`🤖 Active predictions: ${predictionCount}`);
    
    if (predictionCount === 0) {
      addLog('⏳ Waiting for AI predictions...');
    }
  };

  const checkAIStatus = () => {
    addLog('🤖 Checking AI service status...');

    const hasApiKey = !!(import.meta as any).env?.VITE_DEEPSEEK_API_KEY;
    addLog(`API Key configured: ${hasApiKey ? 'Yes' : 'No'}`, hasApiKey ? 'success' : 'error');

    if (!hasApiKey) {
      addLog('❌ DeepSeek API key missing in .env.local', 'error');
      return;
    }

    addLog('✅ AI service ready for real predictions', 'success');
  };

  const testDataFetching = async () => {
    addLog('🧪 Testing data fetching...');

    try {
      // Test Bitvavo API directly
      const response = await fetch('https://api.bitvavo.com/v2/XRP-EUR/candles?interval=1m&limit=5');
      if (response.ok) {
        const data = await response.json();
        addLog(`✅ Bitvavo API working: ${data.length} candles received`);
        addLog(`Latest candle: ${JSON.stringify(data[0])}`);
      } else {
        addLog(`❌ Bitvavo API error: ${response.status}`, 'error');
      }
    } catch (error) {
      addLog(`❌ Data fetch error: ${error.message}`, 'error');
    }
  };

  const getStatusColor = () => {
    if (!autoTradeEnabled) return 'text-gray-400';
    if (Object.keys(predictions).length === 0) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getStatusIcon = () => {
    if (!autoTradeEnabled) return <Pause size={16} />;
    if (Object.keys(predictions).length === 0) return <Clock size={16} />;
    return <CheckCircle size={16} />;
  };

  return (
    <div className="bg-gray-800/60 rounded-xl p-4 border border-gray-700/30">
      <div className="flex items-center gap-2 mb-4">
        <Bug size={18} className="text-blue-400" />
        <span className="text-sm font-semibold text-gray-200">Auto-Trading Debug</span>
        <div className={`flex items-center gap-1 text-xs ${getStatusColor()}`}>
          {getStatusIcon()}
          <span>{autoTradeEnabled ? 'Active' : 'Disabled'}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
        <div className="bg-gray-900/40 rounded-lg p-3 border border-gray-700/30">
          <div className="text-xs text-gray-400">Status</div>
          <div className={`text-sm font-bold ${getStatusColor()}`}>
            {autoTradeEnabled ? 'Enabled' : 'Disabled'}
          </div>
        </div>
        
        <div className="bg-gray-900/40 rounded-lg p-3 border border-gray-700/30">
          <div className="text-xs text-gray-400">Predictions</div>
          <div className="text-sm font-bold text-white">
            {Object.keys(predictions).length}
          </div>
        </div>
        
        <div className="bg-gray-900/40 rounded-lg p-3 border border-gray-700/30">
          <div className="text-xs text-gray-400">Data Status</div>
          <div className="text-sm font-bold text-white">
            {Object.keys(candleHistories1m).length > 0 ? 'Data Available' : 'No Data'}
          </div>
        </div>
      </div>

      <div className="flex gap-2 mb-4">
        <button
          onClick={testAutoTrading}
          className="flex items-center gap-1 px-3 py-1.5 bg-blue-500/20 border border-blue-500/40 text-blue-300 rounded-lg text-xs hover:bg-blue-500/30 transition"
        >
          <Play size={12} />
          Test System
        </button>
        
        <button
          onClick={checkAIStatus}
          className="flex items-center gap-1 px-3 py-1.5 bg-purple-500/20 border border-purple-500/40 text-purple-300 rounded-lg text-xs hover:bg-purple-500/30 transition"
        >
          <RefreshCw size={12} />
          Check AI Status
        </button>

        <button
          onClick={testDataFetching}
          className="flex items-center gap-1 px-3 py-1.5 bg-orange-500/20 border border-orange-500/40 text-orange-300 rounded-lg text-xs hover:bg-orange-500/30 transition"
        >
          <RefreshCw size={12} />
          Test Data
        </button>
        
        <button
          onClick={() => setIsMonitoring(!isMonitoring)}
          className={`flex items-center gap-1 px-3 py-1.5 rounded-lg text-xs transition ${
            isMonitoring 
              ? 'bg-red-500/20 border border-red-500/40 text-red-300 hover:bg-red-500/30'
              : 'bg-green-500/20 border border-green-500/40 text-green-300 hover:bg-green-500/30'
          }`}
        >
          {isMonitoring ? <Pause size={12} /> : <Play size={12} />}
          {isMonitoring ? 'Stop Monitor' : 'Start Monitor'}
        </button>
        
        <button
          onClick={() => setDebugLogs([])}
          className="flex items-center gap-1 px-3 py-1.5 bg-gray-500/20 border border-gray-500/40 text-gray-300 rounded-lg text-xs hover:bg-gray-500/30 transition"
        >
          <AlertCircle size={12} />
          Clear
        </button>
      </div>

      <div className="bg-black/40 rounded-lg p-3 border border-gray-700/30">
        <div className="text-xs text-gray-400 mb-2">Debug Log</div>
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {debugLogs.length === 0 ? (
            <div className="text-xs text-gray-500">No logs yet. Click "Test System" to start.</div>
          ) : (
            debugLogs.map((log, index) => (
              <div key={index} className="text-xs font-mono text-gray-300">
                {log}
              </div>
            ))
          )}
        </div>
      </div>

      <div className="mt-3 text-xs text-gray-500">
        💡 This debug panel helps monitor auto-trading functionality. 
        Check browser console for detailed logs.
      </div>
    </div>
  );
};

export default AutoTradingDebug;
