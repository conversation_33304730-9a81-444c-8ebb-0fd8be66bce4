
import React from 'react';
import { Asset } from '../types';
import { TRADABLE_ASSET_SYMBOLS } from '../constants';

interface MarketIndicatorsProps {
  assets: Asset[];
}

const MarketIndicators: React.FC<MarketIndicatorsProps> = ({ assets }) => {
  const indicatorAssets = assets.filter(asset => 
      !TRADABLE_ASSET_SYMBOLS.includes(asset.symbol) && asset.price > 0
  );

  return (
    <div className="bg-gray-800/60 rounded-xl p-4 border border-gray-700/30">
      <div className="text-xs uppercase tracking-wider text-gray-400 mb-3">Markt Indicatoren</div>
      <div className="grid grid-cols-2 gap-3">
        {indicatorAssets.length > 0 ? indicatorAssets.map(asset => (
          <div key={asset.symbol} className="bg-gray-800/40 rounded-lg p-3 border border-gray-700/20">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-semibold text-sm">{asset.symbol}</div>
                <div className="text-[10px] text-gray-400">{asset.name}</div>
              </div>
              <div className="text-right">
                <div className="font-mono text-white text-sm">€{asset.price.toFixed(2)}</div>
              </div>
            </div>
          </div>
        )) : (
          <p className="text-center text-sm text-gray-500 py-4 col-span-2">Marktdata wordt geladen...</p>
        )}
      </div>
    </div>
  );
};

export default MarketIndicators;