import React from 'react';
import { Prediction, Portfolio, Trade } from '../types';
import { Activity, Shield, Bell, Gauge, TrendingUp, TrendingDown, PauseCircle } from 'lucide-react';

interface AnalysisMonitorProps {
  predictions: Record<string, Prediction | null>;
  portfolio: Portfolio;
  trades: Trade[];
}



const prettyRegime = (r: string) => r.replaceAll('_', ' ');

const AnalysisMonitor: React.FC<AnalysisMonitorProps> = ({ predictions, portfolio, trades }) => {
  const counts = { total: 0, BUY: 0, SELL: 0, HOLD: 0 };
  const regimeMap = new Map<string, string[]>(); // regime -> [symbols]
  const alertsSet = new Set<string>();
  const riskSet = new Set<string>();
  const kpiSet = new Set<string>();

  (Object.entries(predictions) as Array<[string, Prediction | null]>).forEach(([symbol, pVal]) => {
    if (!pVal) return;
    const p: Prediction = pVal;

    counts.total += 1;
    switch (p.prediction) {
      case 'BUY':
        counts.BUY += 1;
        break;
      case 'SELL':
        counts.SELL += 1;
        break;
      case 'HOLD':
        counts.HOLD += 1;
        break;
    }

    if (p.regime) {
      const key = p.regime;
      regimeMap.set(key, [...(regimeMap.get(key) ?? []), symbol]);
    }
    p.monitoring?.alerts?.forEach(a => alertsSet.add(a));
    p.governance?.riskLimits?.forEach(r => riskSet.add(r));
    p.monitoring?.kpis?.forEach(k => kpiSet.add(k));
  });

  const alerts = Array.from(alertsSet);
  const risks = Array.from(riskSet);
  const kpis = Array.from(kpiSet);
  const lastTrade = trades[0];

  return (
    <div className="bg-gray-800/60 rounded-xl p-4 border border-gray-700/30 space-y-4">
      <div className="flex items-center justify-between">
        <div className="text-xs uppercase tracking-wider text-gray-400">Pro Insights</div>
        <div className="text-[10px] text-gray-500">AI stack overzicht</div>
      </div>

      {/* Signals summary */}
      <div className="grid grid-cols-3 gap-2">
        <div className="bg-gray-800/40 rounded-lg p-3 border border-gray-700/20">
          <div className="flex items-center gap-2 text-green-400"><TrendingUp size={14} /><span className="text-xs">BUY</span></div>
          <div className="text-white font-extrabold text-lg">{counts.BUY}</div>
        </div>
        <div className="bg-gray-800/40 rounded-lg p-3 border border-gray-700/20">
          <div className="flex items-center gap-2 text-red-400"><TrendingDown size={14} /><span className="text-xs">SELL</span></div>
          <div className="text-white font-extrabold text-lg">{counts.SELL}</div>
        </div>
        <div className="bg-gray-800/40 rounded-lg p-3 border border-gray-700/20">
          <div className="flex items-center gap-2 text-yellow-400"><PauseCircle size={14} /><span className="text-xs">HOLD</span></div>
          <div className="text-white font-extrabold text-lg">{counts.HOLD}</div>
        </div>
      </div>

      {/* Regimes */}
      <div className="bg-gray-800/40 rounded-lg p-3 border border-gray-700/20">
        <div className="flex items-center gap-2 text-gray-300 font-semibold text-sm mb-2">
          <Activity size={14} /> Regimes
        </div>
        {regimeMap.size > 0 ? (
          <div className="flex flex-wrap gap-2">
            {Array.from(regimeMap.entries()).map(([regime, symbols]) => (
              <div key={regime} className="bg-gray-800/50 rounded-md px-2 py-1 border border-gray-700/30">
                <div className="text-[11px] text-gray-400">{prettyRegime(regime)}</div>
                <div className="mt-1 flex flex-wrap gap-1">
                  {symbols.map(s => (
                    <span key={s} className="inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[10px] font-medium bg-blue-500/15 text-blue-300">
                      {s.replace('-EUR', '')}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-[12px] text-gray-500">Nog geen regime-informatie beschikbaar.</div>
        )}
      </div>

      {/* Risk & Governance */}
      <div className="bg-gray-800/40 rounded-lg p-3 border border-gray-700/20 space-y-2">
        <div className="flex items-center gap-2 text-gray-300 font-semibold text-sm">
          <Shield size={14} /> Risk limits & Governance
        </div>
        {risks.length > 0 ? (
          <ul className="list-disc list-inside text-xs text-gray-400 space-y-1">
            {risks.map((r, i) => (<li key={i}>{r}</li>))}
          </ul>
        ) : (
          <div className="text-[12px] text-gray-500">Geen expliciete risk limits vanuit AI.</div>
        )}
      </div>

      {/* Monitoring */}
      <div className="bg-gray-800/40 rounded-lg p-3 border border-gray-700/20 space-y-2">
        <div className="flex items-center gap-2 text-gray-300 font-semibold text-sm">
          <Bell size={14} /> Monitoring
        </div>
        <div className="grid grid-cols-2 gap-3">
          <div>
            <div className="text-[11px] text-gray-500 mb-1">Alerts</div>
            {alerts.length > 0 ? (
              <ul className="list-disc list-inside text-xs text-gray-400 space-y-1">
                {alerts.map((a, i) => (<li key={i}>{a}</li>))}
              </ul>
            ) : (
              <div className="text-[12px] text-gray-500">Geen alerts aangestuurd.</div>
            )}
          </div>
          <div>
            <div className="text-[11px] text-gray-500 mb-1">KPI’s</div>
            {kpis.length > 0 ? (
              <ul className="list-disc list-inside text-xs text-gray-400 space-y-1">
                {kpis.map((k, i) => (<li key={i}>{k}</li>))}
              </ul>
            ) : (
              <div className="text-[12px] text-gray-500">Geen KPI’s aangestuurd.</div>
            )}
          </div>
        </div>
      </div>

      {/* Portfolio Snapshot */}
      <div className="bg-gray-800/40 rounded-lg p-3 border border-gray-700/20">
        <div className="flex items-center gap-2 text-gray-300 font-semibold text-sm mb-2">
          <Gauge size={14} /> Portfolio
        </div>
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="bg-gray-800/50 rounded-md p-2 border border-gray-700/30">
            <div className="text-[10px] text-gray-500">Totale Waarde</div>
            <div className="text-white font-bold">€{portfolio.totalValue.toFixed(2)}</div>
          </div>
          <div className="bg-gray-800/50 rounded-md p-2 border border-gray-700/30">
            <div className="text-[10px] text-gray-500">P/L</div>
            <div className={portfolio.pl >= 0 ? 'text-green-400 font-bold' : 'text-red-400 font-bold'}>
              {portfolio.pl >= 0 ? '+' : ''}€{portfolio.pl.toFixed(2)} ({portfolio.plPercent.toFixed(2)}%)
            </div>
          </div>
          <div className="bg-gray-800/50 rounded-md p-2 border border-gray-700/30">
            <div className="text-[10px] text-gray-500">Holdings</div>
            <div className="text-white font-bold">{portfolio.holdings.length}</div>
          </div>
          <div className="bg-gray-800/50 rounded-md p-2 border border-gray-700/30">
            <div className="text-[10px] text-gray-500">Trades</div>
            <div className="text-white font-bold">{trades.length}</div>
          </div>
        </div>
        {lastTrade && (
          <div className="mt-2 text-[11px] text-gray-400">
            Laatste trade: {lastTrade.type} {lastTrade.symbol} @ €{lastTrade.price.toFixed(4)} • {new Date(lastTrade.timestamp).toLocaleTimeString()}
          </div>
        )}
      </div>
    </div>
  );
};

export default AnalysisMonitor;