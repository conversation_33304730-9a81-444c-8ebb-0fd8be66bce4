
import React from 'react';
import { BrainCircuit } from 'lucide-react';

const Header: React.FC = () => {
  return (
    <header className="bg-gray-800/50 backdrop-blur-sm sticky top-0 z-10">
      <div className="container mx-auto px-4 lg:px-6 py-3 flex justify-between items-center">
        <div className="flex items-center gap-3">
          <BrainCircuit className="text-teal-400 h-8 w-8" />
          <h1 className="text-xl md:text-2xl font-bold text-white tracking-tight">
            Crypto Trading <span className="text-teal-400">AI Simulator</span>
          </h1>
        </div>
        <div className="text-xs text-gray-400 font-mono">
            Bitvavo | Scalping Strategy
        </div>
      </div>
    </header>
  );
};

export default Header;