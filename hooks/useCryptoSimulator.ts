
import { useState, useEffect, useCallback } from 'react';
import { Asset, Portfolio, Trade, Holding, CandleHistories, BanditState, AdvisorStats, PendingOutcome, TradeContext } from '../types';
import { INITIAL_BALANCE, TRADING_FEE_PERCENT, INITIAL_ASSETS, B<PERSON>VAVO_TICKER_URL, BITVAVO_CANDLES_BASE_URL, TRADABLE_ASSET_SYMBOLS, BANDIT_EVAL_HORIZON_MS, BANDIT_PRIOR_ALPHA, BANDIT_PRIOR_BETA, STRATEGY_VARIANTS, BANDIT_SUCCESS_PNL_EUR_MIN, ADVISOR_DECAY } from '../constants';
import { initBanditState, selectNextStrategy, updateBanditWithReward } from '../utils/bandit';

const STORAGE_VERSION = 1;
const LS = {
  assets: 'cts_assets_v' + STORAGE_VERSION,
  c1m: 'cts_c1m_v' + STORAGE_VERSION,
  c1h: 'cts_c1h_v' + STORAGE_VERSION,
  trades: 'cts_trades_v' + STORAGE_VERSION,
  portfolio: 'cts_portfolio_v' + STORAGE_VERSION,
  bandit: 'cts_bandit_v' + STORAGE_VERSION,
  advisor: 'cts_advisor_v' + STORAGE_VERSION,
  pending: 'cts_pending_v' + STORAGE_VERSION,
};

function loadJSON<T>(key: string, fallback: T): T {
  try {
    const raw = localStorage.getItem(key);
    if (!raw) return fallback;
    return JSON.parse(raw) as T;
  } catch {
    return fallback;
  }
}

export const useCryptoSimulator = () => {
  const [assets, setAssets] = useState<Asset[]>(INITIAL_ASSETS);
  const [candleHistories1m, setCandleHistories1m] = useState<CandleHistories>({});
  const [candleHistories1h, setCandleHistories1h] = useState<CandleHistories>({});
  const [tradeLog, setTradeLog] = useState<Trade[]>([]);
  const [portfolio, setPortfolio] = useState<Portfolio>({
    balance: INITIAL_BALANCE,
    holdings: [],
    totalValue: INITIAL_BALANCE,
    initialValue: INITIAL_BALANCE,
    pl: 0,
    plPercent: 0,
  });
  const [bandit, setBandit] = useState<BanditState>(() => initBanditState(STRATEGY_VARIANTS, BANDIT_PRIOR_ALPHA, BANDIT_PRIOR_BETA));
  const [advisor, setAdvisor] = useState<AdvisorStats>({ id: 'ai-advisor', weight: 0.5, wins: 0, losses: 0, total: 0 });
  const [pendingOutcomes, setPendingOutcomes] = useState<PendingOutcome[]>([]);

  // Load persisted simulator state on mount
  useEffect(() => {
    try {
      const savedAssets = loadJSON<Asset[]>(LS.assets, []);
      if (savedAssets.length > 0) setAssets(savedAssets);

      const savedC1m = loadJSON<CandleHistories>(LS.c1m, {});
      if (Object.keys(savedC1m).length > 0) setCandleHistories1m(savedC1m);

      const savedC1h = loadJSON<CandleHistories>(LS.c1h, {});
      if (Object.keys(savedC1h).length > 0) setCandleHistories1h(savedC1h);

      const savedPortfolio = loadJSON<Portfolio>(LS.portfolio, {
        balance: INITIAL_BALANCE,
        holdings: [],
        totalValue: INITIAL_BALANCE,
        initialValue: INITIAL_BALANCE,
        pl: 0,
        plPercent: 0,
      });
      if (savedPortfolio && typeof savedPortfolio.balance === 'number') {
        setPortfolio(savedPortfolio);
      }

      const savedTrades = loadJSON<Trade[]>(LS.trades, []);
      if (savedTrades.length > 0) {
        const rehydrated = savedTrades.map(t => ({ ...t, timestamp: new Date(t.timestamp) }));
        setTradeLog(rehydrated);
      }

      const savedBandit = loadJSON<BanditState | null>(LS.bandit, null);
      if (savedBandit && Array.isArray(savedBandit.arms)) {
        setBandit(savedBandit);
      } else {
        setBandit(initBanditState(STRATEGY_VARIANTS, BANDIT_PRIOR_ALPHA, BANDIT_PRIOR_BETA));
      }

      const savedAdvisor = loadJSON<AdvisorStats | null>(LS.advisor, null);
      if (savedAdvisor && typeof savedAdvisor.weight === 'number') {
        setAdvisor(savedAdvisor);
      }

      const savedPending = loadJSON<PendingOutcome[]>(LS.pending, []);
      if (savedPending.length > 0) {
        setPendingOutcomes(savedPending);
      }
    } catch {
      // ignore storage errors
    }
  }, []);

  // Persist simulator state whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(LS.assets, JSON.stringify(assets));
      localStorage.setItem(LS.c1m, JSON.stringify(candleHistories1m));
      localStorage.setItem(LS.c1h, JSON.stringify(candleHistories1h));
      localStorage.setItem(LS.portfolio, JSON.stringify(portfolio));
      localStorage.setItem(LS.trades, JSON.stringify(tradeLog));
      localStorage.setItem(LS.bandit, JSON.stringify(bandit));
      localStorage.setItem(LS.advisor, JSON.stringify(advisor));
      localStorage.setItem(LS.pending, JSON.stringify(pendingOutcomes));
    } catch {
      // ignore storage errors
    }
  }, [assets, candleHistories1m, candleHistories1h, portfolio, tradeLog, bandit, advisor, pendingOutcomes]);

  const fetchCandlesForAsset = async (symbol: string, interval: '1m' | '1h') => {
      // Bitvavo REST: GET /v2/{market}/candles?interval=INTERVAL&limit=100
      const url = `${BITVAVO_CANDLES_BASE_URL}/${encodeURIComponent(symbol)}/candles?interval=${interval}&limit=100`;
      const response = await fetch(url);
      if (!response.ok) {
          console.error(`Bitvavo ${interval} Candles API error for ${symbol}: ${response.status} ${response.statusText}`);
          return [];
      }
      const rawCandles: [number, string, string, string, string, string][] = await response.json();
      return rawCandles.map(c => ({
        time: interval === '1m' ? new Date(c[0]).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : new Date(c[0]).toLocaleString(),
        open: parseFloat(c[1]),
        high: parseFloat(c[2]),
        low: parseFloat(c[3]),
        close: parseFloat(c[4]),
        volume: parseFloat(c[5]),
      })).reverse(); // Reverse to have oldest first
  };

  const fetchLiveData = useCallback(async () => {
    console.log('[Data] Fetching live data from Bitvavo...');

    // Add timeout to prevent hanging
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
        const candlePromises = TRADABLE_ASSET_SYMBOLS.flatMap(symbol => [
            fetchCandlesForAsset(symbol, '1m').then(data => ({ symbol, interval: '1m', data })),
            fetchCandlesForAsset(symbol, '1h').then(data => ({ symbol, interval: '1h', data })),
        ]);
        
        const tickerResponsePromise = fetch(BITVAVO_TICKER_URL, { signal: controller.signal });
        const [tickerResponse, ...candleResults] = await Promise.all([tickerResponsePromise, ...candlePromises]);

        clearTimeout(timeoutId); // Clear timeout on success

        const newCandleHistories1m: CandleHistories = {};
        const newCandleHistories1h: CandleHistories = {};
        const pricesFromCandles = new Map<string, number>();

        for (const result of candleResults) {
            if (result.data.length > 0) {
                if (result.interval === '1m') {
                    newCandleHistories1m[result.symbol] = result.data;
                    pricesFromCandles.set(result.symbol, result.data[result.data.length - 1].close);
                } else {
                    newCandleHistories1h[result.symbol] = result.data;
                }
            }
        }
        
        setCandleHistories1m(prev => ({ ...prev, ...newCandleHistories1m }));
        setCandleHistories1h(prev => ({ ...prev, ...newCandleHistories1h }));

        console.log('[Data] Candle data updated:', {
          symbols: Object.keys(newCandleHistories1m),
          counts1m: Object.fromEntries(Object.entries(newCandleHistories1m).map(([k, v]) => [k, v.length])),
          counts1h: Object.fromEntries(Object.entries(newCandleHistories1h).map(([k, v]) => [k, v.length]))
        });

        // Log sample data for debugging
        Object.entries(newCandleHistories1m).forEach(([symbol, candles]) => {
          if (candles.length > 0) {
            console.log(`[Data] ${symbol} latest 1m candle:`, candles[candles.length - 1]);
          }
        });

        const pricesFromTickers = new Map<string, number>();
        if (tickerResponse.ok) {
            const tickers: any[] = await tickerResponse.json();
            tickers.forEach(ticker => {
              const p = (ticker && (ticker.last ?? ticker.price)) as string | undefined;
              const num = p ? parseFloat(p) : NaN;
              if (!isNaN(num)) pricesFromTickers.set(ticker.market, num);
            });
        } else {
            console.error(`Bitvavo Ticker API error: ${tickerResponse.status} ${tickerResponse.statusText}`);
        }
        
        setAssets(prevAssets =>
            prevAssets.map(asset => {
                const newPrice = pricesFromCandles.get(asset.symbol) ?? pricesFromTickers.get(asset.symbol);
                return newPrice ? { ...asset, price: newPrice } : asset;
            })
        );

    } catch (error) {
      console.error("[Data] Failed to fetch live data from Bitvavo:", error);
      throw error; // Re-throw error instead of using mock data
    }
  }, []);

  useEffect(() => {
    console.log('[Data] Setting up data fetching intervals...');
    fetchLiveData(); // Fetch on initial load
    const priceInterval = setInterval(() => {
      console.log('[Data] Interval triggered - fetching data...');
      fetchLiveData();
    }, 30000); // Fetch new data every 30 seconds
    return () => {
      console.log('[Data] Cleaning up data fetching interval');
      clearInterval(priceInterval);
    };
  }, []); // Remove fetchLiveData dependency to prevent constant restarts

  useEffect(() => {
    // Update portfolio value with new prices
    setPortfolio(prevPortfolio => {
      let holdingsValue = 0;
      const updatedHoldings = prevPortfolio.holdings.map(holding => {
        const asset = assets.find(a => a.symbol === holding.symbol);
        const value = asset ? holding.amount * asset.price : 0;
        holdingsValue += value;
        return { ...holding, value };
      });
 
      const totalValue = prevPortfolio.balance + holdingsValue;
      const pl = totalValue - prevPortfolio.initialValue;
      const plPercent = prevPortfolio.initialValue === 0 ? 0 : (pl / prevPortfolio.initialValue) * 100;
 
      return {
        ...prevPortfolio,
        holdings: updatedHoldings,
        totalValue,
        pl,
        plPercent,
      };
    });
  }, [assets]);

  // Evaluate pending trade outcomes after the horizon and update bandit/advisor
  const evaluatePendingOutcomes = useCallback(() => {
    const now = Date.now();
    if (!pendingOutcomes.length) return;

    const due: PendingOutcome[] = [];
    const future: PendingOutcome[] = [];
    for (const p of pendingOutcomes) {
      if (p.evaluateAt <= now) due.push(p);
      else future.push(p);
    }
    if (!due.length) return;

    let nextBandit = bandit;
    let nextAdvisor = advisor;

    for (const p of due) {
      const asset = assets.find(a => a.symbol === p.symbol);
      if (!asset || !isFinite(asset.price)) {
        // Try again later if price unavailable
        future.push(p);
        continue;
      }
      const directionalPnl = (p.direction === 'BUY' ? (asset.price - p.entryPrice) : (p.entryPrice - asset.price)) * p.amount;
      const success = directionalPnl > BANDIT_SUCCESS_PNL_EUR_MIN;
      const rewardW = success ? (nextAdvisor?.weight ?? 0.5) : 0.0;

      // Ensure a selected strategy exists
      if (!nextBandit.selectedStrategyId) {
        nextBandit = selectNextStrategy(nextBandit);
      }
      const sid = p.strategyId ?? nextBandit.selectedStrategyId!;
      nextBandit = updateBanditWithReward(nextBandit, sid, rewardW);

      if (p.adviceId) {
        const total = (nextAdvisor.total ?? 0) + 1;
        const wins = nextAdvisor.wins + (success ? 1 : 0);
        const losses = nextAdvisor.losses + (success ? 0 : 1);
        const newWeight = ADVISOR_DECAY * nextAdvisor.weight + (1 - ADVISOR_DECAY) * (success ? 1 : 0);
        nextAdvisor = { ...nextAdvisor, total, wins, losses, weight: newWeight, lastUpdateAt: now };
      }
    }

    setBandit(nextBandit);
    setAdvisor(nextAdvisor);
    setPendingOutcomes(future);
  }, [pendingOutcomes, assets, bandit, advisor]);

  useEffect(() => {
    if (pendingOutcomes.length === 0) return;
    const timer = setInterval(() => {
      evaluatePendingOutcomes();
    }, 10000); // check every 10s
    return () => clearInterval(timer);
  }, [pendingOutcomes.length, evaluatePendingOutcomes]);

  const performTrade = useCallback((asset: Asset, type: 'BUY' | 'SELL', amount: number, context?: TradeContext) => {
    if (asset.price === 0) {
        console.warn(`Trade for ${asset.symbol} blocked because price is 0.`);
        return; // Do not trade if price is not loaded
    }

    // Ensure we have a selected strategy
    let strategyId = bandit.selectedStrategyId;
    if (!strategyId) {
      const next = selectNextStrategy(bandit);
      setBandit(next);
      strategyId = next.selectedStrategyId!;
    }
    const adviceId = context?.adviceId;
    const usedStrategyId = context?.strategyId ?? strategyId;

    setPortfolio(prevPortfolio => {
      const currentHoldings = prevPortfolio.holdings.find(h => h.symbol === asset.symbol)?.amount ?? 0;
      const newPortfolio = { ...prevPortfolio };
      let newHoldings: Holding[] = [...prevPortfolio.holdings];
      
      const price = asset.price;
      const total = amount * price;
      const fee = total * TRADING_FEE_PERCENT;
      let profit: number | undefined = undefined;
 
      if (type === 'BUY' && newPortfolio.balance >= total + fee) {
        newPortfolio.balance -= (total + fee);
        const existingHolding = newHoldings.find(h => h.symbol === asset.symbol);
        if (existingHolding) {
          existingHolding.amount += amount;
        } else {
          newHoldings.push({ symbol: asset.symbol, amount: amount, value: total });
        }
      } else if (type === 'SELL' && currentHoldings >= amount) {
        newPortfolio.balance += (total - fee);
        
        const previousBuys = tradeLog.filter(t => t.symbol === asset.symbol && t.type === 'BUY');
        if (previousBuys.length > 0) {
            const avgBuyPrice = previousBuys.reduce((sum, t) => sum + t.price, 0) / previousBuys.length;
            profit = (price - avgBuyPrice) * amount - fee;
        }
 
        const existingHolding = newHoldings.find(h => h.symbol === asset.symbol);
        if (existingHolding) {
          existingHolding.amount -= amount;
          if (existingHolding.amount < 0.00001) { // clean up small dust
            newHoldings = newHoldings.filter(h => h.symbol !== asset.symbol);
          }
        }
      } else {
        return prevPortfolio; // Invalid trade, return original state
      }
 
      const newTrade: Trade = {
        id: new Date().toISOString(),
        symbol: asset.symbol,
        type,
        amount,
        price,
        total,
        fee,
        timestamp: new Date(),
        profit,
        strategyId: usedStrategyId,
        adviceId: adviceId,
      };
 
      setTradeLog(prevLog => [newTrade, ...prevLog.slice(0, 99)]);

      // Schedule an outcome evaluation for the bandit after the horizon
      setPendingOutcomes(prev => [
        ...prev,
        {
          id: newTrade.id,
          tradeId: newTrade.id,
          symbol: asset.symbol,
          direction: type,
          entryPrice: price,
          amount,
          strategyId: usedStrategyId,
          adviceId: adviceId,
          evaluateAt: Date.now() + BANDIT_EVAL_HORIZON_MS,
        }
      ]);
      
      newPortfolio.holdings = newHoldings;
      return newPortfolio;
    });
  }, [bandit, tradeLog]);

  const simulateTrade = useCallback((asset: Asset, type: 'BUY' | 'SELL', amount: number) => {
    performTrade(asset, type, amount);
  }, [performTrade]);
  
  const resetSimulation = useCallback(() => {
    // Reset only simulator state; keep live market data and candles (learned/observed context)
    setTradeLog([]);
    setPortfolio({
      balance: INITIAL_BALANCE,
      holdings: [],
      totalValue: INITIAL_BALANCE,
      initialValue: INITIAL_BALANCE,
      pl: 0,
      plPercent: 0,
    });
  }, []);
  
  return {
    assets,
    portfolio,
    tradeLog,
    simulateTrade,
    resetSimulation,
    candleHistories1m,
    candleHistories1h,
    bandit,
    selectedStrategyId: bandit.selectedStrategyId,
    strategyVariants: STRATEGY_VARIANTS,
    advisorWeight: advisor.weight,
    advisor,
  };
};