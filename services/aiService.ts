import { CandleDataPoint, Trade, Portfolio, Prediction } from '../types';
import { TRADING_FEE_PERCENT, DEEPSEEK_API_KEY, DEEPSEEK_API_URL } from "../constants";

// Single-provider AI service: DeepSeek only


export const getTradingAdvice = async (
  assetSymbol: string,
  candleHistory1m: CandleDataPoint[],
  candleHistory1h: CandleDataPoint[],
  tradeLog: Trade[],
  portfolio: Portfolio
): Promise<Prediction> => {
  if (!DEEPSEEK_API_KEY) {
    throw new Error("DeepSeek API key missing. Configure VITE_DEEPSEEK_API_KEY in your .env.local");
  }

  console.log(`[AI] Getting trading advice for ${assetSymbol}...`);

  const deepseekPrompt = `
Je bent een deskundige crypto trading bot genaamd 'ScalperAI', gespecialiseerd in korte-termijn scalping strategieën. <PERSON> doel is om veel kleine winsten te behalen door data-ged<PERSON>ven beslissingen te nemen.

TAAK: Voer een holistische analyse uit op basis van de verstrekte data en geef een handelsadvies voor ${assetSymbol} voor de komende 30 minuten.

Pro tradingstack (verkort):
- Regime-detectie, signalen (korte vs. lange termijn), executie (TWAP/VWAP/Limit/Market), risico (sizing/stops), monitoring (alerts/KPI’s), governance (risk limits).

CONTEXT:
- Handelsstrategie: Scalping met oog voor de trend.
- Data: 1-minuut en 1-uur kandelaardata (geen live nieuws beschikbaar).
- Huidige Portfolio Waarde: €${portfolio.totalValue.toFixed(2)}
- Winst/Verlies: €${portfolio.pl.toFixed(2)} (${portfolio.plPercent.toFixed(2)}%)
- Bitvavo Handelskosten: ${(TRADING_FEE_PERCENT * 100).toFixed(2)}%.

DATA ANALYSE:

1) LANGE TERMIJN CONTEXT (1-Uur - ${assetSymbol}):
${candleHistory1h.map(c => `[T: ${c.time}, O: ${c.open.toFixed(4)}, H: ${c.high.toFixed(4)}, L: ${c.low.toFixed(4)}, C: ${c.close.toFixed(4)}]`).join('\n')}

2) KORTE TERMIJN ACTIE (1-Minuut - ${assetSymbol}):
${candleHistory1m.map(c => `[T: ${c.time}, O: ${c.open.toFixed(5)}, H: ${c.high.toFixed(5)}, L: ${c.low.toFixed(5)}, C: ${c.close.toFixed(5)}]`).join('\n')}

3) ZELF-REFLECTIE (Recente Trades):
${tradeLog.length > 0 ? tradeLog.slice(-5).map(t => `${t.type} ${t.amount.toFixed(2)} ${t.symbol} @ €${t.price.toFixed(4)}`).join('\n') : 'Nog geen transacties.'}

XRP-specifiek (alleen wanneer ${assetSymbol} == 'XRP-EUR'):
- Escrow/ODL/XRPL/regulatie in 'xrpNotes' indien bekend. Laat anders weg.

ADVIES & OUTPUT:
- Geef enkel een schoon JSON-object (geen code fences) volgens onderstaand schema. Alle pro-velden optioneel. 'newsSummary' moet de vaste notitie bevatten dat nieuws niet beschikbaar is. Gebruik decimale waarden (confidence 0..1).

{
  "prediction": "BUY|SELL|HOLD",
  "confidence": 0.0,
  "reasoning": "Synthese gebaseerd op grafiekdata.",
  "strategyAdjustment": "Aanpassing n.a.v. zelfreflectie.",
  "newsSummary": "Nieuwsanalyse niet beschikbaar bij gebruik van Deepseek AI.",
  "regime": "trend_up|trend_down|range|risk_off",
  "horizon": "short_term|long_term",
  "signalsUsed": [
    { "name": "VWAP/AVWAP", "why": "institutionele referentie", "strength": 0.5 }
  ],
  "executionPlan": {
    "style": "Limit",
    "urgency": "medium",
    "maxSlippageBps": 10
  },
  "positionSizing": {
    "pctOfPortfolio": 10,
    "stopLoss": { "type": "level", "value": 0.02 },
    "takeProfit": { "rr": 2.0 }
  },
  "monitoring": { "alerts": ["regime-wissel", "AVWAP-touch"] },
  "governance": { "riskLimits": ["dagverlieslimiet 2%"] },
  "xrpNotes": "Alleen wanneer toepasselijk (XRP-EUR).",
  "dataSummary": ["1h trend", "1m timing"]
}
`;

  const response = await fetch(DEEPSEEK_API_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${DEEPSEEK_API_KEY}`
    },
    body: JSON.stringify({
      model: 'deepseek-chat',
      messages: [{ role: 'user', content: deepseekPrompt }],
      temperature: 0.3
    })
  });

  if (!response.ok) {
    const errorBody = await response.text();
    throw new Error(`DeepSeek API Error: ${response.status} ${response.statusText} - ${errorBody}`);
  }

  const data = await response.json();
  const jsonText = data.choices[0]?.message?.content?.trim().replace(/```json\n?|\n?```/g, '') ?? '{}';
  console.log(`[AI] Raw response for ${assetSymbol}:`, jsonText);
  const parsedResponse = JSON.parse(jsonText);

  if (
    typeof parsedResponse.prediction !== 'string' ||
    !['BUY', 'SELL', 'HOLD'].includes(parsedResponse.prediction)
  ) {
    throw new Error("DeepSeek response is malformed or missing prediction.");
  }

  return parsedResponse as Prediction;
};

