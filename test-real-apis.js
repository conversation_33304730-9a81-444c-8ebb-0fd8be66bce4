// Test script voor echte API integratie
// Run dit in de browser console

console.log('🧪 Testing Real API Integration...');

// Test 1: Bitvavo API
async function testBitvavoAPI() {
  console.log('\n📊 Testing Bitvavo API...');
  
  try {
    const response = await fetch('https://api.bitvavo.com/v2/XRP-EUR/candles?interval=1m&limit=3');
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Bitvavo API working:', data.length, 'candles received');
      console.log('Sample candle:', data[0]);
      return true;
    } else {
      console.error('❌ Bitvavo API error:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Bitvavo API error:', error);
    return false;
  }
}

// Test 2: DeepSeek API via proxy
async function testDeepSeekAPI() {
  console.log('\n🤖 Testing DeepSeek API via proxy...');
  
  try {
    const response = await fetch('/api/deepseek/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-53366adb35ce46b78c2d0983af0522d9'
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [{ 
          role: 'user', 
          content: 'Respond with exactly this JSON: {"prediction": "BUY", "confidence": 0.75, "reasoning": "Test successful"}' 
        }],
        temperature: 0.1,
        max_tokens: 100
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ DeepSeek API working via proxy');
      console.log('Response:', data);
      
      const content = data.choices[0]?.message?.content;
      if (content) {
        try {
          const parsed = JSON.parse(content);
          console.log('✅ AI response parsed successfully:', parsed);
          return true;
        } catch (e) {
          console.error('❌ AI response JSON parse error:', e);
          return false;
        }
      }
    } else {
      const errorText = await response.text();
      console.error('❌ DeepSeek API error:', response.status, errorText);
      return false;
    }
  } catch (error) {
    console.error('❌ DeepSeek API error:', error);
    return false;
  }
}

// Test 3: Check app data status
function checkAppDataStatus() {
  console.log('\n📈 Checking app data status...');
  
  const keys = [
    'cts_assets_v1',
    'cts_c1m_v1',
    'cts_c1h_v1',
    'cts_predictions_v1'
  ];
  
  let hasData = false;
  
  keys.forEach(key => {
    const data = localStorage.getItem(key);
    if (data) {
      try {
        const parsed = JSON.parse(data);
        if (typeof parsed === 'object' && parsed !== null) {
          if (Array.isArray(parsed)) {
            console.log(`✅ ${key}: Array with ${parsed.length} items`);
            if (parsed.length > 0) hasData = true;
          } else {
            const itemCount = Object.keys(parsed).length;
            console.log(`✅ ${key}: Object with ${itemCount} keys`);
            if (itemCount > 0) hasData = true;
          }
        }
      } catch (e) {
        console.log(`❌ ${key}: Parse error`);
      }
    } else {
      console.log(`❌ ${key}: No data`);
    }
  });
  
  return hasData;
}

// Test 4: Monitor for real AI predictions
function monitorRealPredictions() {
  console.log('\n🔍 Monitoring for real AI predictions...');
  
  let lastPredictions = {};
  let checkCount = 0;
  const maxChecks = 20; // Monitor for 2 minutes (20 * 6 seconds)
  
  const monitor = setInterval(() => {
    checkCount++;
    
    const predictions = JSON.parse(localStorage.getItem('cts_predictions_v1') || '{}');
    const predictionCount = Object.keys(predictions).length;
    
    if (predictionCount > 0) {
      const newPredictions = Object.entries(predictions).filter(([symbol, prediction]) => {
        return prediction && (!lastPredictions[symbol] || 
               JSON.stringify(prediction) !== JSON.stringify(lastPredictions[symbol]));
      });
      
      if (newPredictions.length > 0) {
        console.log('🎉 New real AI predictions detected!');
        newPredictions.forEach(([symbol, prediction]) => {
          console.log(`${symbol}: ${prediction.prediction} (confidence: ${prediction.confidence})`);
          console.log(`Reasoning: ${prediction.reasoning}`);
        });
        
        clearInterval(monitor);
        console.log('✅ Real AI predictions are working!');
        return;
      }
    }
    
    if (checkCount >= maxChecks) {
      clearInterval(monitor);
      console.log('⏰ Monitoring timeout - no predictions received in 2 minutes');
      console.log('💡 This might be normal if data is still being collected or AI is processing');
    } else {
      console.log(`⏳ Checking for predictions... (${checkCount}/${maxChecks})`);
    }
    
    lastPredictions = { ...predictions };
  }, 6000); // Check every 6 seconds
  
  console.log('🔄 Started monitoring for 2 minutes...');
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting comprehensive real API tests...\n');
  
  const bitvavoOK = await testBitvavoAPI();
  const deepseekOK = await testDeepSeekAPI();
  const hasAppData = checkAppDataStatus();
  
  console.log('\n📋 Test Results Summary:');
  console.log(`Bitvavo API: ${bitvavoOK ? '✅ Working' : '❌ Failed'}`);
  console.log(`DeepSeek API: ${deepseekOK ? '✅ Working' : '❌ Failed'}`);
  console.log(`App Data: ${hasAppData ? '✅ Available' : '❌ No data yet'}`);
  
  if (bitvavoOK && deepseekOK) {
    console.log('\n🎯 All APIs working! Starting prediction monitoring...');
    monitorRealPredictions();
  } else {
    console.log('\n❌ Some APIs failed. Check the errors above.');
  }
}

// Export to global scope
window.realAPITest = {
  testBitvavoAPI,
  testDeepSeekAPI,
  checkAppDataStatus,
  monitorRealPredictions,
  runAllTests
};

console.log('✅ Real API Test Script loaded!');
console.log('📝 Available functions:');
console.log('  - realAPITest.runAllTests() - Run complete test suite');
console.log('  - realAPITest.testDeepSeekAPI() - Test AI API only');
console.log('  - realAPITest.monitorRealPredictions() - Monitor for predictions');
console.log('\n🚀 Run realAPITest.runAllTests() to start!');
