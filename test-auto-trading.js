// Test script om auto-trading functionaliteit te controleren
// Run dit in de browser console wanneer de app draait

console.log('🔍 Auto-Trading Test Script gestart...');

// Functie om de huidige app state te controleren
function checkAppState() {
  console.log('\n📊 Huidige App State:');
  
  // Check localStorage voor belangrijke data
  const autoTradeEnabled = localStorage.getItem('cts_auto_trade_enabled_v1');
  const portfolio = localStorage.getItem('cts_portfolio_v1');
  const trades = localStorage.getItem('cts_trades_v1');
  const predictions = localStorage.getItem('cts_predictions_v1');
  const lastActions = localStorage.getItem('cts_last_actions_v1');
  
  console.log('Auto Trade Enabled:', autoTradeEnabled);
  console.log('Portfolio:', portfolio ? JSON.parse(portfolio) : 'Geen data');
  console.log('Trades Count:', trades ? JSON.parse(trades).length : 0);
  console.log('Predictions:', predictions ? Object.keys(JSON.parse(predictions)).length : 0);
  console.log('Last Actions:', lastActions ? JSON.parse(lastActions) : 'Geen data');
}

// Functie om te controleren of er AI adviezen worden gegenereerd
function monitorAIAdvice() {
  console.log('\n🤖 Monitoring AI Advice...');
  
  const checkPredictions = () => {
    const predictions = localStorage.getItem('cts_predictions_v1');
    if (predictions) {
      const parsed = JSON.parse(predictions);
      Object.entries(parsed).forEach(([symbol, prediction]) => {
        if (prediction) {
          console.log(`${symbol}: ${prediction.prediction} (confidence: ${prediction.confidence})`);
        }
      });
    }
  };
  
  // Check elke 5 seconden
  setInterval(checkPredictions, 5000);
  checkPredictions(); // Direct uitvoeren
}

// Functie om trades te monitoren
function monitorTrades() {
  console.log('\n💰 Monitoring Trades...');
  
  let lastTradeCount = 0;
  
  const checkTrades = () => {
    const trades = localStorage.getItem('cts_trades_v1');
    if (trades) {
      const parsed = JSON.parse(trades);
      if (parsed.length > lastTradeCount) {
        console.log(`🎉 Nieuwe trade gedetecteerd! Total trades: ${parsed.length}`);
        const latestTrade = parsed[0]; // Nieuwste trade staat vooraan
        console.log('Latest trade:', latestTrade);
        lastTradeCount = parsed.length;
      }
    }
  };
  
  // Check elke 2 seconden
  setInterval(checkTrades, 2000);
  checkTrades(); // Direct uitvoeren
}

// Functie om portfolio wijzigingen te monitoren
function monitorPortfolio() {
  console.log('\n📈 Monitoring Portfolio...');
  
  let lastBalance = null;
  
  const checkPortfolio = () => {
    const portfolio = localStorage.getItem('cts_portfolio_v1');
    if (portfolio) {
      const parsed = JSON.parse(portfolio);
      if (lastBalance !== null && parsed.balance !== lastBalance) {
        console.log(`💸 Balance changed: €${lastBalance} → €${parsed.balance}`);
        console.log('Holdings:', parsed.holdings);
      }
      lastBalance = parsed.balance;
    }
  };
  
  // Check elke 3 seconden
  setInterval(checkPortfolio, 3000);
  checkPortfolio(); // Direct uitvoeren
}

// Functie om de DeepSeek API status te controleren
function checkAPIStatus() {
  console.log('\n🔑 Checking API Status...');
  
  // Check of API key is ingesteld
  const hasApiKey = window.location.href.includes('localhost') || 
                   document.querySelector('[data-api-status]') !== null;
  
  console.log('API Key configured:', hasApiKey);
  
  // Monitor voor API errors in console
  const originalError = console.error;
  console.error = function(...args) {
    if (args.some(arg => typeof arg === 'string' && 
        (arg.includes('DeepSeek') || arg.includes('API') || arg.includes('429')))) {
      console.log('🚨 API Error detected:', ...args);
    }
    originalError.apply(console, args);
  };
}

// Functie om auto-trading te forceren (voor testing)
function forceAutoTrade() {
  console.log('\n🚀 Force Auto-Trade Test...');
  
  // Simuleer een BUY advies
  const testPrediction = {
    prediction: 'BUY',
    confidence: 0.8,
    reasoning: 'Test advies voor auto-trading',
    strategyAdjustment: 'Test',
    positionSizing: { pctOfPortfolio: 10 }
  };
  
  // Sla test prediction op
  const predictions = JSON.parse(localStorage.getItem('cts_predictions_v1') || '{}');
  predictions['XRP-EUR'] = testPrediction;
  localStorage.setItem('cts_predictions_v1', JSON.stringify(predictions));
  
  console.log('Test prediction toegevoegd voor XRP-EUR');
  console.log('Auto-trading zou nu moeten triggeren als het enabled is...');
}

// Start alle monitoring functies
function startFullTest() {
  console.log('🎯 Starting Full Auto-Trading Test Suite...');
  
  checkAppState();
  checkAPIStatus();
  monitorAIAdvice();
  monitorTrades();
  monitorPortfolio();
  
  console.log('\n✅ Test suite gestart! Check de console voor updates...');
  console.log('💡 Tip: Run forceAutoTrade() om een test trade te triggeren');
}

// Export functies naar global scope
window.autoTradingTest = {
  checkAppState,
  monitorAIAdvice,
  monitorTrades,
  monitorPortfolio,
  checkAPIStatus,
  forceAutoTrade,
  startFullTest
};

console.log('✅ Auto-Trading Test Script geladen!');
console.log('📝 Beschikbare functies:');
console.log('  - autoTradingTest.startFullTest() - Start volledige test');
console.log('  - autoTradingTest.checkAppState() - Check huidige state');
console.log('  - autoTradingTest.forceAutoTrade() - Force een test trade');
console.log('\n🚀 Run autoTradingTest.startFullTest() om te beginnen!');
