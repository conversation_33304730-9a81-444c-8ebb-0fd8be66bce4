import React, { useState, useEffect, useCallback } from 'react';
import { useCryptoSimulator } from './hooks/useCryptoSimulator';
import { getTradingAdvice } from './services/aiService';
import { Prediction, AppState } from './types';
import Header from './components/Header';
import PortfolioPanel from './components/PortfolioPanel';
import TradeLog from './components/TradeLog';
import MarketIndicators from './components/MarketIndicators';
import AssetSummaryCard from './components/AssetSummaryCard';
import { TRADABLE_ASSET_SYMBOLS } from './constants';
import AnalysisMonitor from './components/AnalysisMonitor';
import AutoTradingDebug from './components/AutoTradingDebug';
import DataStatusDebug from './components/DataStatusDebug';

const STORAGE_VERSION = 1;
const LS = {
  predictions: 'cts_predictions_v' + STORAGE_VERSION,
  analysisStates: 'cts_analysis_states_v' + STORAGE_VERSION,
  lastErrors: 'cts_last_errors_v' + STORAGE_VERSION,
  analysisQueue: 'cts_analysis_queue_v' + STORAGE_VERSION,
  autoTradeEnabled: 'cts_auto_trade_enabled_v' + STORAGE_VERSION,
  lastActions: 'cts_last_actions_v' + STORAGE_VERSION,
  lastAdviceAt: 'cts_last_advice_at_v' + STORAGE_VERSION,
  profile: 'cts_profile_v' + STORAGE_VERSION,
};

// Auto-trading configuration (more active defaults)
const DEFAULT_MIN_EUR_TRADE = 5;
const DEFAULT_CONFIDENCE_THRESHOLD = 0.5;
const DEFAULT_SYMBOL_COOLDOWN_MS = 3 * 60 * 1000; // 3 minutes
// High-frequency AI advice cadence (per symbol)
const ADVICE_INTERVAL_MS = 30000; // 30 seconds


type LastAction = {
  last: 'BUY' | 'SELL' | null;
  time: number; // epoch ms
  price: number | null;
};

function loadJSON<T>(key: string, fallback: T): T {
  try {
    const raw = localStorage.getItem(key);
    if (!raw) return fallback;
    return JSON.parse(raw) as T;
  } catch {
    return fallback;
  }
}

const App: React.FC = () => {
  const { assets, portfolio, tradeLog, simulateTrade, resetSimulation, candleHistories1m, candleHistories1h, bandit, advisor } = useCryptoSimulator();

  const [analysisStates, setAnalysisStates] = useState<Record<string, AppState>>(() =>
    loadJSON<Record<string, AppState>>(LS.analysisStates, Object.fromEntries(TRADABLE_ASSET_SYMBOLS.map(s => [s, AppState.Monitoring])))
  );
  const [predictions, setPredictions] = useState<Record<string, Prediction | null>>(() =>
    loadJSON<Record<string, Prediction | null>>(LS.predictions, {})
  );
  const [lastErrors, setLastErrors] = useState<Record<string, string | null>>(() =>
    loadJSON<Record<string, string | null>>(LS.lastErrors, {})
  );

  // Auto-trading toggles and memory
  const [autoTradeEnabled, setAutoTradeEnabled] = useState<boolean>(() =>
    loadJSON<boolean>(LS.autoTradeEnabled, true)
  );
  const [lastActions, setLastActions] = useState<Record<string, LastAction>>(() =>
    loadJSON<Record<string, LastAction>>(LS.lastActions, {})
  );
  // Last advice timestamps per symbol + simple profiles
  const [lastAdviceAt, setLastAdviceAt] = useState<Record<string, number>>(() => loadJSON<Record<string, number>>(LS.lastAdviceAt, {}));
  const [profile, setProfile] = useState<'conservative'|'balanced'|'aggressive'>(() => loadJSON(LS.profile, 'aggressive'));


  // New state for managing a sequential analysis queue to avoid rate limiting
  const [analysisQueue, setAnalysisQueue] = useState<string[]>(() =>
    loadJSON<string[]>(LS.analysisQueue, [])
  );

  // User-tunable settings (persisted)
  const [minEurTrade, setMinEurTrade] = useState<number>(() => loadJSON<number>(LS.autoTradeEnabled + '_min', DEFAULT_MIN_EUR_TRADE));
  const [confidenceThreshold, setConfidenceThreshold] = useState<number>(() => loadJSON<number>(LS.autoTradeEnabled + '_conf', DEFAULT_CONFIDENCE_THRESHOLD));
  const [symbolCooldownMs, setSymbolCooldownMs] = useState<number>(() => loadJSON<number>(LS.autoTradeEnabled + '_cooldown', DEFAULT_SYMBOL_COOLDOWN_MS));


  // Persist app-level state so the app resumes after refresh/reopen
  useEffect(() => {
    try {
      localStorage.setItem(LS.predictions, JSON.stringify(predictions));
      localStorage.setItem(LS.autoTradeEnabled + '_min', JSON.stringify(minEurTrade));
      localStorage.setItem(LS.autoTradeEnabled + '_conf', JSON.stringify(confidenceThreshold));
      localStorage.setItem(LS.autoTradeEnabled + '_cooldown', JSON.stringify(symbolCooldownMs));
      localStorage.setItem(LS.analysisStates, JSON.stringify(analysisStates));
      localStorage.setItem(LS.lastErrors, JSON.stringify(lastErrors));
      localStorage.setItem(LS.analysisQueue, JSON.stringify(analysisQueue));
      localStorage.setItem(LS.autoTradeEnabled, JSON.stringify(autoTradeEnabled));
      localStorage.setItem(LS.lastActions, JSON.stringify(lastActions));
      localStorage.setItem(LS.lastAdviceAt, JSON.stringify(lastAdviceAt));
      localStorage.setItem(LS.profile, JSON.stringify(profile));
    } catch {
      // ignore storage errors
    }
  }, [predictions, analysisStates, lastErrors, analysisQueue, autoTradeEnabled, lastActions, lastAdviceAt, profile]);

  // Reset simulation: clears portfolio and tradeLog; preserves candles/predictions ("geleerd" context)
  const onReset = useCallback(() => {
    const ok = window.confirm('Reset simulatie? Portfolio en transactielog worden gewist, analyses en candles blijven bewaard.');
    if (!ok) return;
    resetSimulation();
    setLastErrors({});
    // Clear predictions so AI bouwt opnieuw op
    setPredictions({});
    // Normalize analysis state so symbols continue monitoring instead of getting stuck
    setAnalysisStates(prev => {
      const next = { ...prev };
      TRADABLE_ASSET_SYMBOLS.forEach(s => { next[s] = AppState.Monitoring; });
      return next;
    });
  }, [resetSimulation]);



  // Create a stable handleAnalysis function
  const handleAnalysis = useCallback(async (symbol: string) => {
    const currentHistories1m = candleHistories1m[symbol] ?? [];
    const currentHistories1h = candleHistories1h[symbol] ?? [];

    console.log(`[AutoTrade] ${symbol} - 1m candles: ${currentHistories1m.length}, 1h candles: ${currentHistories1h.length}, state: ${analysisStates[symbol]}`);

    // Check if we have sufficient data
    if (currentHistories1m.length < 10 || currentHistories1h.length < 3) {
      console.log(`[AutoTrade] ${symbol} - Skipping analysis: insufficient data (1m: ${currentHistories1m.length}, 1h: ${currentHistories1h.length})`);
      return;
    }

    // Check if already analyzing (but allow retry after timeout)
    if (analysisStates[symbol] === AppState.Analyzing) {
      console.log(`[AutoTrade] ${symbol} - Already analyzing, skipping...`);
      return;
    }

    setAnalysisStates(prev => ({ ...prev, [symbol]: AppState.Analyzing }));
    setLastErrors(prev => ({ ...prev, [symbol]: null }));

    try {
      console.log(`[AutoTrade] ${symbol} - Starting AI analysis...`);
      const advice = await getTradingAdvice(symbol, currentHistories1m, currentHistories1h, tradeLog, portfolio);
      console.log(`[AutoTrade] ${symbol} - AI advice received:`, advice.prediction, 'confidence:', advice.confidence);

      setPredictions(prev => ({ ...prev, [symbol]: advice }));
      setLastAdviceAt(prev => ({ ...prev, [symbol]: Date.now() }));

      const assetToTrade = assets.find(a => a.symbol === symbol);

      // Execute autonomous trade only if enabled and asset is known
      console.log(`[AutoTrade] ${symbol} - autoTradeEnabled: ${autoTradeEnabled}, assetFound: ${!!assetToTrade}, advice: ${advice.prediction}, confidence: ${advice.confidence}`);
      if (autoTradeEnabled && assetToTrade) {
        const conf = advice.confidence ?? 0;
        const now = Date.now();
        const la = lastActions[symbol] ?? { last: null, time: 0, price: null };

        // Helper to update last action state
        const commitLastAction = (typ: 'BUY' | 'SELL', price: number | null) => {
          setLastActions(prev => ({
            ...prev,
            [symbol]: { last: typ, time: now, price }
          }));
        };

        // Cooldown check
        const underCooldown = now - (la.time ?? 0) < symbolCooldownMs;
        console.log(`[AutoTrade] ${symbol} advice=${advice.prediction} conf=${conf.toFixed(2)} cooldown=${underCooldown} last=${la.last} price=${assetToTrade.price}`);

        // BUY path
        if (advice.prediction === 'BUY') {
          if (!underCooldown || la.last !== 'BUY') {
            if (conf >= confidenceThreshold) {
              const pct = Math.max(1, Math.min(100, advice.positionSizing?.pctOfPortfolio ?? 25));
              const eurToSpend = Math.min(portfolio.balance, portfolio.balance * (pct / 100));
              if (assetToTrade.price > 0 && eurToSpend >= minEurTrade) {
                console.log(`[AutoTrade] BUY ${symbol} eur=${eurToSpend.toFixed(2)} @ ${assetToTrade.price}`);
                simulateTrade(assetToTrade, 'BUY', eurToSpend / assetToTrade.price);
                commitLastAction('BUY', assetToTrade.price);
              } else {
                console.log(`[AutoTrade] BUY skipped for ${symbol}: price=${assetToTrade.price} eurToSpend=${eurToSpend.toFixed(2)} (min=${minEurTrade})`);
              }
            } else {
              console.log(`[AutoTrade] BUY skipped for ${symbol}: confidence ${conf.toFixed(2)} < threshold ${confidenceThreshold}`);
            }
          } else {
            console.log(`[AutoTrade] BUY skipped for ${symbol}: under cooldown and last was BUY`);
          }
        }

        // SELL path
        if (advice.prediction === 'SELL') {
          if (!underCooldown || la.last !== 'SELL') {
            if (conf >= confidenceThreshold) {
              const assetBalance = portfolio.holdings.find(h => h.symbol === symbol)?.amount ?? 0;
              const price = assetToTrade.price;
              const sellAmount = assetBalance * 0.75; // conservative partial exit
              if (price > 0 && sellAmount > 0 && (sellAmount * price) >= minEurTrade) {
                console.log(`[AutoTrade] SELL ${symbol} amount=${sellAmount.toFixed(6)} ~€${(sellAmount*price).toFixed(2)} @ ${price}`);
                simulateTrade(assetToTrade, 'SELL', sellAmount);
                commitLastAction('SELL', price);
              } else {
                console.log(`[AutoTrade] SELL skipped for ${symbol}: price=${price} amount=${sellAmount.toFixed(6)} (value ~€${(sellAmount*price).toFixed(2)})`);
              }
            } else {
              console.log(`[AutoTrade] SELL skipped for ${symbol}: confidence ${conf.toFixed(2)} < threshold ${confidenceThreshold}`);
            }
          } else {
            console.log(`[AutoTrade] SELL skipped for ${symbol}: under cooldown and last was SELL`);
          }
        }
      }

      setAnalysisStates(prev => ({ ...prev, [symbol]: AppState.Monitoring }));
    } catch (error) {
      console.error(`Error getting trading advice for ${symbol}:`, error);
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred.";

      if (errorMessage.includes('429') || errorMessage.includes('RESOURCE_EXHAUSTED') || errorMessage.includes('quota')) {
        setLastErrors(prev => ({ ...prev, [symbol]: `API-limiet bereikt. Volgende poging in cyclus.` }));
      } else {
        setLastErrors(prev => ({ ...prev, [symbol]: `Analyse mislukt.` }));
      }
      setAnalysisStates(prev => ({ ...prev, [symbol]: AppState.Error }));
    }
  }, [candleHistories1m, candleHistories1h, analysisStates, tradeLog, portfolio, assets, autoTradeEnabled, lastActions, confidenceThreshold, minEurTrade, symbolCooldownMs, simulateTrade]);

  // Switch to per-symbol interval cadence (30s) – disable queue population
  useEffect(() => {
    setAnalysisQueue([]);
  }, []);

  // Reset any stuck analyzing states on mount
  useEffect(() => {
    const currentStates = { ...analysisStates };
    let hasStuckStates = false;

    TRADABLE_ASSET_SYMBOLS.forEach(symbol => {
      if (currentStates[symbol] === AppState.Analyzing) {
        currentStates[symbol] = AppState.Monitoring;
        hasStuckStates = true;
      }
    });

    if (hasStuckStates) {
      console.log('[AutoTrade] Resetting stuck analyzing states...');
      setAnalysisStates(currentStates);
    }
  }, []);

  // Per-symbol 30s cadence: trigger analysis every ADVICE_INTERVAL_MS per tradable symbol
  useEffect(() => {
    console.log('[AutoTrade] Setting up analysis intervals...');

    const timers = TRADABLE_ASSET_SYMBOLS.map((symbol, index) => {
      const tick = () => {
        console.log(`[AutoTrade] Triggering analysis for ${symbol} at ${new Date().toLocaleTimeString()}`);
        handleAnalysis(symbol);
      };
      // Trigger immediately on mount with a small delay to spread the load
      setTimeout(tick, index * 1000);
      return setInterval(tick, ADVICE_INTERVAL_MS);
    });

    return () => {
      console.log('[AutoTrade] Cleaning up analysis intervals');
      timers.forEach(clearInterval);
    };
  }, [handleAnalysis]);


  return (
    <div className="min-h-screen bg-gray-900 text-gray-100 font-sans">
      <Header />
      <main className="container mx-auto px-4 lg:px-6 py-6 space-y-6">
        {/* KPI's */}
        <section className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-gray-800/60 rounded-xl p-4 border border-gray-700/30">
            <div className="text-xs text-gray-400">Totale Waarde</div>
            <div className="text-2xl font-extrabold text-white tracking-tight">€{portfolio.totalValue.toFixed(2)}</div>
          </div>
          <div className="bg-gray-800/60 rounded-xl p-4 border border-gray-700/30">
            <div className="text-xs text-gray-400">P/L</div>
            <div className={`text-2xl font-extrabold ${portfolio.pl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {portfolio.pl >= 0 ? '+' : ''}€{portfolio.pl.toFixed(2)} ({portfolio.plPercent.toFixed(2)}%)
            </div>
          </div>
          <div className="bg-gray-800/60 rounded-xl p-4 border border-gray-700/30">
            <div className="text-xs text-gray-400">Saldo</div>
            <div className="text-2xl font-extrabold text-white">€{portfolio.balance.toFixed(2)}</div>
          </div>
          <div className="bg-gray-800/60 rounded-xl p-4 border border-gray-700/30">
            <div className="text-xs text-gray-400">Activa • Trades</div>
            <div className="text-2xl font-extrabold text-white">{portfolio.holdings.length} • {tradeLog.length}</div>
          </div>
        </section>

        {/* AI Health & Settings */}
        <section className="bg-gray-800/60 rounded-xl p-4 border border-gray-700/30">
          <div className="flex flex-col gap-4">
            <div className="flex items-start justify-between gap-4 flex-wrap">
              <div>
                <div className="text-xs text-gray-400">DeepSeek Status</div>
                <div className="text-sm mt-0.5">
                  <span className={String(((import.meta as any).env?.VITE_DEEPSEEK_API_KEY) ? 'text-green-400' : 'text-red-400')}>
                    {((import.meta as any).env?.VITE_DEEPSEEK_API_KEY) ? 'API key ingesteld' : 'API key ontbreekt'}
                  </span>
                  {Object.values(lastAdviceAt).length > 0 && (() => {
                    const times = Object.values(lastAdviceAt) as number[];
                    const latest = Math.max(...times);
                    return (
                      <span className="ml-2 text-gray-300">• Laatste advies: {new Date(latest).toLocaleTimeString()}</span>
                    );
                  })()}

                </div>
              </div>

              <div className="flex items-center gap-2 flex-wrap">
                <label className="text-xs text-gray-400 flex items-center gap-1">
                  Profiel
                  <select
                    value={profile}
                    onChange={(e) => {
                      const p = e.target.value as 'conservative'|'balanced'|'aggressive';
                      setProfile(p);
                      if (p === 'conservative') {
                        setConfidenceThreshold(0.65);
                        setSymbolCooldownMs(10 * 60000);
                        setMinEurTrade(15);
                      } else if (p === 'balanced') {
                        setConfidenceThreshold(0.55);
                        setSymbolCooldownMs(8 * 60000);
                        setMinEurTrade(10);
                      } else {
                        setConfidenceThreshold(0.5);
                        setSymbolCooldownMs(5 * 60000);
                        setMinEurTrade(10);
                      }
                    }}
                    className="bg-gray-900 border border-gray-700 rounded px-2 py-1 w-36 text-gray-200"
                  >
                    <option value="conservative">Conservative</option>
                    <option value="balanced">Balanced</option>
                    <option value="aggressive">Aggressive</option>
                  </select>
                </label>

                <label className="text-xs text-gray-400 flex items-center gap-1">
                  Min €/trade
                  <input type="number" min={1} step={1} value={minEurTrade}
                    onChange={e => setMinEurTrade(Math.max(1, Number(e.target.value) || DEFAULT_MIN_EUR_TRADE))}
                    className="bg-gray-900 border border-gray-700 rounded px-2 py-1 w-20 text-gray-200" />
                </label>
                <label className="text-xs text-gray-400 flex items-center gap-1">
                  Confidence
                  <input type="number" min={0} max={1} step={0.01} value={confidenceThreshold}
                    onChange={e => setConfidenceThreshold(Math.min(1, Math.max(0, Number(e.target.value))))}
                    className="bg-gray-900 border border-gray-700 rounded px-2 py-1 w-20 text-gray-200" />
                </label>
                <label className="text-xs text-gray-400 flex items-center gap-1">
                  Cooldown (min)
                  <input type="number" min={0} step={1} value={Math.round(symbolCooldownMs/60000)}
                    onChange={e => setSymbolCooldownMs(Math.max(0, Number(e.target.value) * 60000))}
                    className="bg-gray-900 border border-gray-700 rounded px-2 py-1 w-24 text-gray-200" />
                </label>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div className="bg-gray-900/40 rounded-lg p-3 border border-gray-700/30">
                <div className="text-xs text-gray-400">AI Vertrouwen</div>
                <div className="text-lg font-bold">{(advisor?.weight ?? 0.5).toFixed(2)}</div>
                <div className="text-[11px] text-gray-400">Wins: {advisor?.wins ?? 0} • Losses: {advisor?.losses ?? 0} • Total: {advisor?.total ?? 0}</div>
              </div>
              <div className="bg-gray-900/40 rounded-lg p-3 border border-gray-700/30">
                <div className="text-xs text-gray-400">Strategieën (Bandit)</div>
                <div className="text-[11px] text-gray-300">
                  {((bandit?.arms ?? []).length === 0) ? (
                    <div className="text-gray-500">Geen strategie-data beschikbaar</div>
                  ) : (
                    (bandit?.arms ?? []).map(a => {
                      const total = (a.wins ?? 0) + (a.losses ?? 0);
                      const winRate = total > 0 ? a.wins / total : 0;
                      const ev = (a.alpha + a.beta) > 0 ? a.alpha / (a.alpha + a.beta) : 0.5;
                      const sel = bandit?.selectionCountById?.[a.id] ?? 0;
                      return (
                        <div key={a.id} className="flex items-center justify-between py-1.5 border-b border-gray-800/60 last:border-0">
                          <div className="min-w-0">
                            <div className="text-xs font-semibold text-gray-200">{a.id}</div>
                            <div className="mt-1 w-32 h-1.5 bg-gray-700/50 rounded">
                              <div className="h-1.5 bg-blue-500 rounded" style={{ width: `${Math.round(ev*100)}%` }} />
                            </div>
                          </div>
                          <div className="flex items-center gap-3 text-[11px] text-gray-400">
                            <span>Sel: <span className="text-gray-200">{sel}</span></span>
                            <span>Pulls: <span className="text-gray-200">{a.pulls}</span></span>
                            <span>W/L: <span className="text-gray-200">{a.wins.toFixed(2)}</span>/<span className="text-gray-200">{a.losses.toFixed(2)}</span></span>
                            <span>WR: <span className="text-gray-200">{(winRate*100).toFixed(0)}%</span></span>
                            <span>EV: <span className="text-gray-200">{ev.toFixed(2)}</span></span>
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
              <div className="bg-gray-900/40 rounded-lg p-3 border border-gray-700/30">
                <div className="text-xs text-gray-400">Leren in woorden</div>
                <div className="text-[11px] text-gray-300">
                  Het systeem past de strategie-keuze aan met Thompson Sampling op basis van gerealiseerde uitkomsten (na horizon). Succesvolle adviezen verhogen het AI-vertrouwen (0..1), mislukte verlagen het.
                </div>
              </div>
            </div>

            <div>
              <div className="text-xs text-gray-400 mb-2">Laatste AI-advies per symbool</div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                {TRADABLE_ASSET_SYMBOLS.map(s => (
                  <div key={s} className="bg-gray-900/40 rounded-md p-2 border border-gray-700/30 flex items-center justify-between">
                    <span className="text-gray-300">{s}</span>
                    <span className="text-gray-400">{lastAdviceAt[s] ? new Date(lastAdviceAt[s]).toLocaleTimeString() : '—'}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>


        {/* Data Status Debug Panel */}
        <section>
          <DataStatusDebug
            candleHistories1m={candleHistories1m}
            candleHistories1h={candleHistories1h}
            assets={assets}
            predictions={predictions}
          />
        </section>

        {/* Auto-Trading Debug Panel */}
        <section>
          <AutoTradingDebug
            autoTradeEnabled={autoTradeEnabled}
            predictions={predictions}
            lastActions={lastActions}
            portfolio={portfolio}
            assets={assets}
            candleHistories1m={candleHistories1m}
            candleHistories1h={candleHistories1h}
            onToggleAutoTrade={() => setAutoTradeEnabled(v => !v)}
          />
        </section>

        {/* Trading Controls */}
        <section className="bg-gray-800/60 rounded-xl p-4 border border-gray-700/30">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-xs text-gray-400">Autonoom Handelen</div>
              <div className={`text-sm font-bold ${autoTradeEnabled ? 'text-green-400' : 'text-gray-300'}`}>{autoTradeEnabled ? 'Aan' : 'Uit'}</div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setAutoTradeEnabled(v => !v)}
                className={`px-3 py-1.5 rounded-lg text-sm font-semibold border transition
                  ${autoTradeEnabled ? 'bg-green-500/10 border-green-500/40 text-green-300 hover:bg-green-500/20' : 'bg-gray-700/50 border-gray-600 text-gray-200 hover:bg-gray-700'}`}
                aria-pressed={autoTradeEnabled}
              >
                {autoTradeEnabled ? 'Uitschakelen' : 'Inschakelen'}
              </button>
              <button
                onClick={onReset}
                className="px-3 py-1.5 rounded-lg text-sm font-semibold border transition bg-gray-700/50 border-gray-600 text-gray-200 hover:bg-gray-700"
                aria-label="Reset simulatie (portfolio & transacties wissen)"
              >
                Reset Simulatie
              </button>
            </div>
          </div>
          <div className="mt-2 text-[11px] text-gray-500">
            Advies-interval: {Math.round(ADVICE_INTERVAL_MS/1000)}s per symbool • Cooldown per symbool: {Math.round(symbolCooldownMs/60000)}m • Min trade: €{minEurTrade}
          </div>
        </section>

        {/* Activa overzicht */}
        <section>
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
            {TRADABLE_ASSET_SYMBOLS.map(symbol => {
              const isDataSufficient = (candleHistories1m[symbol]?.length ?? 0) >= 10 && (candleHistories1h[symbol]?.length ?? 0) >= 3;
              const price = assets.find(a => a.symbol === symbol)?.price ?? 0;
              return (
                <AssetSummaryCard
                  key={symbol}
                  symbol={symbol}
                  price={price}
                  candleHistory={candleHistories1m[symbol] ?? []}
                  prediction={predictions[symbol] ?? null}
                  status={analysisStates[symbol]}
                  error={lastErrors[symbol] ?? null}
                  isDataSufficient={isDataSufficient}
                  lastAdviceAt={lastAdviceAt[symbol]}
                />
              );
            })}
          </div>
        </section>

        {/* Portfolio en zijpaneel */}
        <section className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <div className="lg:col-span-2">
            <PortfolioPanel portfolio={portfolio} />
          </div>
          <div className="space-y-4">
            <AnalysisMonitor predictions={predictions} portfolio={portfolio} trades={tradeLog} />
            <MarketIndicators assets={assets} />
            <TradeLog trades={tradeLog.slice(0, 8)} />
          </div>
        </section>
      </main>
    </div>
  );
};

export default App;