
export interface Asset {
  symbol: string;
  name: string;
  price: number;
  priceChange24h: number;
  correlation?: number;
}

export interface Holding {
  symbol: string;
  amount: number;
  value: number; // in EUR
}

export interface Portfolio {
  balance: number; // EUR
  holdings: Holding[];
  totalValue: number; // balance + holdings value
  initialValue: number;
  pl: number; // Profit/Loss
  plPercent: number;
}

export interface TradeContext {
  strategyId?: string;
  adviceId?: string;
}

export interface Trade {
  id: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  amount: number; // amount of crypto
  price: number; // price per unit in EUR
  total: number; // total cost/gain in EUR
  fee: number; // fee in EUR
  timestamp: Date;
  profit?: number; // Profit realized on this trade if it's a SELL
  // Traceability (optional so existing UI keeps working)
  strategyId?: string;
  adviceId?: string;
}

export interface CandleDataPoint {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export type CandleHistories = Record<string, CandleDataPoint[]>;

// ---- Pro Lite Trading Stack Types (all optional in Prediction for backward compatibility) ----
export type Horizon = 'short_term' | 'long_term';

export interface SignalUsed {
  name: string;
  why: string;
  strength?: number; // 0..1
  notes?: string;
}

export interface ExecutionPlan {
  style?: 'TWAP' | 'VWAP' | 'Limit' | 'Market';
  urgency?: 'low' | 'medium' | 'high';
  maxSlippageBps?: number;
  participationRatePct?: number; // 0..100
  notes?: string;
}

export interface StopDef {
  type: 'atr' | 'level';
  value: number;
}

export interface TakeProfitDef {
  rr?: number; // risk-reward ratio
  targetPrice?: number;
}

export interface PositionSizing {
  pctOfPortfolio?: number; // 0..100
  rationale?: string;
  stopLoss?: StopDef;
  takeProfit?: TakeProfitDef;
  hedge?: string;
}

export interface MonitoringPlan {
  alerts?: string[];
  kpis?: string[];
  anomalyDetectors?: string[];
}

export interface GovernanceNotes {
  riskLimits?: string[];
  complianceNotes?: string[];
}

export interface Prediction {
  prediction: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  reasoning: string;
  strategyAdjustment: string;

  // News/sources remain optional
  newsSummary?: string;
  sources?: { uri: string; title: string }[];

  // Pro Lite optional fields
  regime?: string; // e.g., trend_up, trend_down, range, risk_off
  horizon?: Horizon; // short_term | long_term
  signalsUsed?: SignalUsed[];
  executionPlan?: ExecutionPlan;
  positionSizing?: PositionSizing;
  monitoring?: MonitoringPlan;
  governance?: GovernanceNotes;
  xrpNotes?: string; // when applicable for XRP-EUR
  dataSummary?: string[]; // bullets mapping to pro data & feeds
}

// ---- Bandit / Strategy selection types ----
export interface StrategyVariant {
  id: string;           // stable identifier
  name: string;         // human readable
  params: Record<string, number | string | boolean>; // tunables
}

export interface BanditArmState {
  id: string;    // matches StrategyVariant.id
  alpha: number; // Beta prior alpha (successes + prior)
  beta: number;  // Beta prior beta (failures + prior)
  pulls: number;
  wins: number;
  losses: number;
  lastSelectedAt?: number;
}

export interface BanditState {
  arms: BanditArmState[];
  selectedStrategyId?: string;
  selectionCountById: Record<string, number>;
  lastUpdateAt?: number;
}

export interface AdviceSnapshot {
  id: string;
  symbol: string;
  createdAt: number;
  prediction: 'BUY' | 'SELL' | 'HOLD';
  confidence: number; // 0..1
}

export interface AdvisorStats {
  id: 'ai-advisor';
  // simple rolling weight 0..1 representing trust
  weight: number;
  wins: number;
  losses: number;
  total: number;
  lastUpdateAt?: number;
}

export interface PendingOutcome {
  id: string; // link id
  tradeId: string;
  symbol: string;
  direction: 'BUY' | 'SELL';
  entryPrice: number;
  amount: number;
  strategyId?: string;
  adviceId?: string;
  evaluateAt: number; // timestamp in ms
}

export enum AppState {
    Initializing = 'INITIALIZING',
    Monitoring = 'MONITORING',
    Analyzing = 'ANALYZING',
    Error = 'ERROR',
}